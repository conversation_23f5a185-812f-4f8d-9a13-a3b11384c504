import 'dart:async';

import 'package:e_trader/src/domain/model/price_alert.dart';
import 'package:e_trader/src/domain/model/set_price_alert_model.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/domain/usecase/delete_price_alert_use_case.dart';
import 'package:e_trader/src/domain/usecase/modify_price_alert_use_case.dart';
import 'package:e_trader/src/domain/usecase/save_price_alert_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_symbol_quotes_use_case.dart';
import 'package:e_trader/src/presentation/price_alert/edit_price_alert/edit_price_alert_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

part 'set_price_alert_event.dart';
part 'set_price_alert_state.dart';
part 'set_price_alert_bloc.freezed.dart';

class SetPriceAlertBloc extends Bloc<SetPriceAlertEvent, SetPriceAlertState> {
  final SavePriceAlertUseCase _savePriceAlertUseCase;
  final ModifyPriceAlertUseCase _modifyPriceAlertUseCase;

  final DeletePriceAlertUseCase _deletePriceAlertUseCase;
  final SubscribeToSymbolQuotesUseCase _subscribeToSymbolQuotesUseCase;

  SetPriceAlertBloc(
    this._savePriceAlertUseCase,
    this._subscribeToSymbolQuotesUseCase,
    this._deletePriceAlertUseCase,
    this._modifyPriceAlertUseCase,
  ) : super(SetPriceAlertState.loading()) {
    on<_OnSaveAlert>(_onSaveAlert);
    on<_OnEditPriceChanged>(_onEditPriceStateChanged);
    on<_OnFetchSymbolDetails>(_onFetchSymbolDetails);
    on<_TradeTypeChanged>(_onTradeTypeChanged);
    on<_OnDeleteAlert>(_onDeleteAlert);
    on<_OnModifyAlert>(_onModifyAlert);
  }

  void _onEditPriceStateChanged(
    _OnEditPriceChanged event,
    Emitter<SetPriceAlertState> emit,
  ) {
    switch (event.editState) {
      case EditPriceAlertSuccess(info: final model):
        switch (state) {
          case SetPriceAlertSuccess(:final info):
            emit(
              SetPriceAlertState.success(
                info.copyWith(
                  enteredPrice: model.enteredPrice,
                  vaidationError: model.validationErrorMessage != null,
                ),
              ),
            );
          case _:
            break;
        }
      case _:
        break;
    }
  }

  void _onTradeTypeChanged(
    _TradeTypeChanged event,
    Emitter<SetPriceAlertState> emit,
  ) {
    if (state case SetPriceAlertSuccess(:final info)) {
      emit(
        SetPriceAlertState.success(info.copyWith(selctedTradeType: event.type)),
      );
    }
  }

  FutureOr<void> _onSaveAlert(
    _OnSaveAlert event,
    Emitter<SetPriceAlertState> emit,
  ) async {
    if (state case SetPriceAlertSuccess(:final info)) {
      emit(
        SetPriceAlertState.success(
          info.copyWith(viewState: SetPriceAlertViewState.creating),
        ),
      );
      var saveResponse =
          await _savePriceAlertUseCase(
            symbolCode: info.prices.platformName,
            triggerPrice: info.enteredPrice.toStringAsFixed(info.prices.digits),
            type: info.selctedTradeType,
          ).run();
      await saveResponse.fold(
        (exception) async {
          addError(exception);
          emit(
            SetPriceAlertState.success(
              info.copyWith(viewState: SetPriceAlertViewState.creationFailed),
            ),
          );

          await Future<void>.delayed(const Duration(seconds: 2), () {
            emit(
              SetPriceAlertState.success(
                info.copyWith(viewState: SetPriceAlertViewState.idle),
              ),
            );
          });
        },
        (response) async {
          emit(
            SetPriceAlertState.success(
              info.copyWith(viewState: SetPriceAlertViewState.creationSuccess),
            ),
          );
          await Future<void>.delayed(const Duration(seconds: 2), () {
            emit(
              SetPriceAlertState.success(
                info.copyWith(viewState: SetPriceAlertViewState.idle),
              ),
            );
          });
        },
      );
    }
  }

  void _onDeleteAlert(
    _OnDeleteAlert event,
    Emitter<SetPriceAlertState> emit,
  ) async {
    if (state case SetPriceAlertSuccess(:final info)) {
      emit(
        SetPriceAlertState.success(
          info.copyWith(viewState: SetPriceAlertViewState.deleting),
        ),
      );

      var saveResponse =
          await _deletePriceAlertUseCase(
            alertIds: [event.alert.priceAlertId],
          ).run();
      await saveResponse.fold(
        (exception) async {
          addError(exception);
          emit(
            SetPriceAlertState.success(
              info.copyWith(viewState: SetPriceAlertViewState.deleteFailed),
            ),
          );

          await Future<void>.delayed(const Duration(seconds: 2), () {
            emit(
              SetPriceAlertState.success(
                info.copyWith(viewState: SetPriceAlertViewState.idle),
              ),
            );
          });
        },
        (response) async {
          emit(
            SetPriceAlertState.success(
              info.copyWith(viewState: SetPriceAlertViewState.deleteSuccess),
            ),
          );

          await Future<void>.delayed(const Duration(seconds: 2), () {
            emit(
              SetPriceAlertState.success(
                info.copyWith(viewState: SetPriceAlertViewState.idle),
              ),
            );
          });
        },
      );
    }
  }

  void _onModifyAlert(
    _OnModifyAlert event,
    Emitter<SetPriceAlertState> emit,
  ) async {
    if (state case SetPriceAlertSuccess(:final info)) {
      emit(
        SetPriceAlertState.success(
          info.copyWith(viewState: SetPriceAlertViewState.modifying),
        ),
      );
      var saveResponse =
          await _modifyPriceAlertUseCase(
            alertId: event.alert.priceAlertId,
            triggerPrice: info.enteredPrice.toStringAsFixed(info.prices.digits),
            type: info.selctedTradeType,
          ).run();
      await saveResponse.fold(
        (exception) async {
          addError(exception);
          emit(
            SetPriceAlertState.success(
              info.copyWith(
                viewState: SetPriceAlertViewState.modificationFailed,
              ),
            ),
          );

          await Future<void>.delayed(const Duration(seconds: 2), () {
            emit(
              SetPriceAlertState.success(
                info.copyWith(viewState: SetPriceAlertViewState.idle),
              ),
            );
          });
        },
        (response) async {
          emit(
            SetPriceAlertState.success(
              info.copyWith(
                viewState: SetPriceAlertViewState.modificationSuccess,
              ),
            ),
          );
          await Future<void>.delayed(const Duration(seconds: 2), () {
            emit(
              SetPriceAlertState.success(
                info.copyWith(viewState: SetPriceAlertViewState.idle),
              ),
            );
          });
        },
      );
    }
  }

  FutureOr<void> _onFetchSymbolDetails(
    _OnFetchSymbolDetails event,
    Emitter<SetPriceAlertState> emit,
  ) async {
    //
    emit(SetPriceAlertState.loading());
    try {
      final result =
          await TaskEither.sequenceList([
            _subscribeToSymbolQuotesUseCase(
              symbol: event.symbol,
              subscriberId: '${SetPriceAlertBloc}_$hashCode',
            ),
          ]).run();
      return await result.fold(
        (left) {
          addError(left);
          emit(SetPriceAlertState.error());
        },
        (onRight) {
          final subscribeResultStream = onRight.elementAtOrNull(0);
          return emit.forEach(
            subscribeResultStream!,
            onData: (symbolQuoteModel) {
              final newModel = switch (state) {
                SetPriceAlertSuccess(:final info) => info.copyWith(
                  prices: symbolQuoteModel,
                ),
                _ => () {
                  final retModel = SetPriceAlertModel(
                    selctedTradeType:
                        event.alert?.alertTradeType ?? TradeType.buy,
                    prices: symbolQuoteModel,
                    enteredPrice:
                        event.alert?.priceAlertPrice ?? symbolQuoteModel.ask,
                    viewState: SetPriceAlertViewState.idle,
                    vaidationError: false,
                  );
                  return retModel;
                }(),
              };

              return SetPriceAlertState.success(newModel);
            },
          );
        },
      );
    } catch (e) {
      addError(e);
      if (!isClosed) emit(SetPriceAlertState.error());
    }
  }
}
