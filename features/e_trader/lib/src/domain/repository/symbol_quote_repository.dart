import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/pool/symbol_quote_model_pool.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

class SymbolQuoteRepository {
  SocketClient socketClient;
  final SymbolQuoteModelPool _modelPool = SymbolQuoteModelPool();

  SymbolQuoteRepository({required this.socketClient});

  TaskEither<Exception, Stream<SymbolQuoteModel>> subscribeToSymbolQuotes({
    required String symbol,
    required String accountNumber,
    required String subscriberId,
  }) {
    return socketClient
        .subscribe(
          path: 'productHub',
          eventType: TradingSocketEvent.quotes.subscribe,
          targets: ["QuotesUpdated"],
          subscriberId: subscriberId,
          args: [
            {
              "accountNumber": accountNumber,
              "symbols": [symbol],
            },
          ],
        )
        .map(
          (result) => result
              .map((data) {
                final arguments =
                    (data as Map<String, dynamic>)['arguments']
                        as Map<String, dynamic>;

                // Use pool instead of creating new instances
                final nSymbol = arguments['symbol'] as String;
                return _modelPool.getOrCreate(nSymbol, arguments);
              })
              .where((data) => data.platformName == symbol),
        );
  }

  TaskEither<Exception, Stream<SymbolQuoteModel>> connectToSymbolsQuotes({
    required String subscriberId,
  }) => socketClient
      .subscribe(
        path: 'productHub',
        eventType: TradingSocketEvent.quotes.register,
        targets: ['QuotesUpdated'],
        subscriberId: subscriberId,
      )
      .map(
        (stream) => stream.map((data) {
          final arguments =
              (data as Map<String, dynamic>)['arguments']
                  as Map<String, dynamic>;

          final symbol = arguments['symbol'] as String;
          // Use pool for all symbols
          return _modelPool.getOrCreate(symbol, arguments);
        }),
      );

  Future<void> updateQuotesBySymbols(
    List<String> symbols,
    EventType eventType,
    String accountNumber,
  ) {
    return socketClient.updateSubscription(
      path: 'productHub',
      eventType: eventType,
      targets: ['QuotesUpdated'],
      args: [
        {"accountNumber": accountNumber, "symbols": symbols},
      ],
    );
  }
}
